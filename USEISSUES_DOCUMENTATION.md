# useIssues Hook Documentation

## Overview
The `useIssues` hook is a comprehensive React hook that provides all the functionality needed to manage issues in your application using Supabase as the backend. It's designed as a drop-in replacement for the original Zustand-based issues store from the Circle project.

## Features
- ✅ Complete CRUD operations for issues
- ✅ Real-time updates via Supabase subscriptions
- ✅ Advanced filtering and searching
- ✅ Optimistic updates for better UX
- ✅ LexoRank support for drag-and-drop ordering
- ✅ Label management
- ✅ Status and priority management
- ✅ Project and assignee management
- ✅ Loading states and error handling
- ✅ TypeScript support with full type safety

## Installation & Setup

### 1. Database Setup
First, run the SQL scripts from `SUPABASE_ISSUES_SCHEMA.md` to set up your database tables and policies.

### 2. Import the Hook
```typescript
import { useIssues } from './path/to/useIssues';
```

### 3. Basic Usage
```typescript
function IssuesComponent() {
  const {
    issues,
    issuesByStatus,
    loading,
    error,
    getAllIssues,
    addIssue,
    updateIssue,
    deleteIssue,
    searchIssues,
    filterIssues,
  } = useIssues();

  if (loading) return <div>Loading issues...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h1>Issues ({issues.length})</h1>
      {issues.map(issue => (
        <div key={issue.id}>
          <h3>{issue.title}</h3>
          <p>{issue.description}</p>
          <span>Status: {issue.status?.name}</span>
        </div>
      ))}
    </div>
  );
}
```

## API Reference

### State Properties

#### `issues: Issue[]`
Array of all issues with complete data including joined relationships.

#### `issuesByStatus: Record<string, Issue[]>`
Issues grouped by status ID for easy rendering in Kanban-style layouts.

#### `loading: boolean`
Loading state for initial data fetch and major operations.

#### `error: string | null`
Error message if any operation fails.

### Data Fetching Functions
These functions return data directly and are safe to call multiple times.

#### `getAllIssues(): Issue[]`
Returns all issues currently in state.

```typescript
const allIssues = getAllIssues();
```

#### `getIssueById(id: string): Issue | undefined`
Returns a specific issue by ID.

```typescript
const issue = getIssueById('issue-id-123');
```

#### `getIssuesByStatus(statusId: string): Issue[]`
Returns all issues with a specific status.

```typescript
const inProgressIssues = getIssuesByStatus('in-progress');
```

#### `searchIssues(query: string): Issue[]`
Searches issues by title, description, and identifier.

```typescript
const searchResults = searchIssues('bug fix');
```

#### `filterIssues(filters: FilterOptions): Issue[]`
Filters issues by multiple criteria.

```typescript
const filteredIssues = filterIssues({
  status: ['in-progress', 'to-do'],
  assignee: ['user-id-1'],
  priority: ['high', 'urgent'],
  labels: ['bug', 'feature'],
  project: ['project-1']
});
```

### Action Functions
These functions return Promises and should be used with async/await or .then()/.catch().

#### `addIssue(issueInput: CreateIssueInput): Promise<Issue>`
Creates a new issue.

```typescript
const handleCreateIssue = async () => {
  try {
    const newIssue = await addIssue({
      title: 'Fix login bug',
      description: 'Users cannot log in with email',
      status_id: 'to-do',
      priority_id: 'high',
      assignee_id: 'user-123',
      rank: 'a0000', // LexoRank string
      labels: ['bug', 'urgent']
    });
    console.log('Issue created:', newIssue);
  } catch (error) {
    console.error('Failed to create issue:', error);
  }
};
```

#### `updateIssue(id: string, updates: Partial<Issue>): Promise<Issue>`
Updates an existing issue.

```typescript
const handleUpdateIssue = async (issueId: string) => {
  try {
    const updatedIssue = await updateIssue(issueId, {
      title: 'Updated title',
      description: 'Updated description'
    });
    console.log('Issue updated:', updatedIssue);
  } catch (error) {
    console.error('Failed to update issue:', error);
  }
};
```

#### `deleteIssue(id: string): Promise<void>`
Deletes an issue.

```typescript
const handleDeleteIssue = async (issueId: string) => {
  try {
    await deleteIssue(issueId);
    console.log('Issue deleted successfully');
  } catch (error) {
    console.error('Failed to delete issue:', error);
  }
};
```

#### `updateIssueStatus(issueId: string, newStatus: Status): Promise<Issue>`
Updates an issue's status (useful for drag-and-drop).

```typescript
const handleStatusChange = async (issueId: string, newStatus: Status) => {
  try {
    const updatedIssue = await updateIssueStatus(issueId, newStatus);
    console.log('Status updated:', updatedIssue);
  } catch (error) {
    console.error('Failed to update status:', error);
  }
};
```

#### `updateIssuePriority(issueId: string, newPriority: Priority): Promise<Issue>`
Updates an issue's priority.

```typescript
const handlePriorityChange = async (issueId: string, newPriority: Priority) => {
  try {
    const updatedIssue = await updateIssuePriority(issueId, newPriority);
    console.log('Priority updated:', updatedIssue);
  } catch (error) {
    console.error('Failed to update priority:', error);
  }
};
```

#### `updateIssueAssignee(issueId: string, assigneeId: string | null): Promise<Issue>`
Updates an issue's assignee.

```typescript
const handleAssigneeChange = async (issueId: string, assigneeId: string | null) => {
  try {
    const updatedIssue = await updateIssueAssignee(issueId, assigneeId);
    console.log('Assignee updated:', updatedIssue);
  } catch (error) {
    console.error('Failed to update assignee:', error);
  }
};
```

#### `addIssueLabel(issueId: string, labelId: string): Promise<void>`
Adds a label to an issue.

```typescript
const handleAddLabel = async (issueId: string, labelId: string) => {
  try {
    await addIssueLabel(issueId, labelId);
    console.log('Label added successfully');
  } catch (error) {
    console.error('Failed to add label:', error);
  }
};
```

#### `removeIssueLabel(issueId: string, labelId: string): Promise<void>`
Removes a label from an issue.

```typescript
const handleRemoveLabel = async (issueId: string, labelId: string) => {
  try {
    await removeIssueLabel(issueId, labelId);
    console.log('Label removed successfully');
  } catch (error) {
    console.error('Failed to remove label:', error);
  }
};
```

#### `updateIssueProject(issueId: string, projectId: string | null): Promise<Issue>`
Updates an issue's project assignment.

```typescript
const handleProjectChange = async (issueId: string, projectId: string | null) => {
  try {
    const updatedIssue = await updateIssueProject(issueId, projectId);
    console.log('Project updated:', updatedIssue);
  } catch (error) {
    console.error('Failed to update project:', error);
  }
};
```

## TypeScript Interfaces

### Core Types
```typescript
interface Issue {
  id: string;
  identifier: string;        // e.g., "ISSUE-1234567890"
  title: string;
  description: string;
  status_id: string;
  assignee_id: string | null;
  priority_id: string;
  project_id: string | null;
  cycle_id: string | null;
  parent_issue_id: string | null;
  rank: string;             // LexoRank for ordering
  due_date: string | null;
  created_at: string;
  updated_at: string;
  created_by: string;
  
  // Joined data
  status?: Status;
  assignee?: User;
  priority?: Priority;
  project?: Project;
  cycle?: Cycle;
  labels?: Label[];
  subissues?: string[];
}

interface CreateIssueInput {
  title: string;
  description?: string;
  status_id: string;
  assignee_id?: string | null;
  priority_id: string;
  project_id?: string | null;
  cycle_id?: string | null;
  parent_issue_id?: string | null;
  rank: string;
  due_date?: string | null;
  labels?: string[];
}

interface FilterOptions {
  status?: string[];
  assignee?: string[];
  priority?: string[];
  labels?: string[];
  project?: string[];
}
```

## Real-time Updates
The hook automatically sets up Supabase real-time subscriptions to listen for changes to:
- Issues table
- Issue labels junction table

When changes occur, the hook automatically refetches the data to keep the UI in sync.

## Error Handling
All action functions return Promises that can be caught for error handling:

```typescript
try {
  await addIssue(newIssueData);
  // Success handling
} catch (error) {
  // Error handling
  console.error('Operation failed:', error);
}
```

## Performance Considerations
- The hook uses optimistic updates for better UX
- Real-time subscriptions are automatically cleaned up on unmount
- Data is cached in component state to minimize database calls
- Filtering and searching happen in memory for fast results

## Migration from Zustand Store
This hook provides the exact same API as the original Zustand store, making migration straightforward:

1. Replace `useIssuesStore()` with `useIssues()`
2. Update any direct state access to use the returned properties
3. Update action calls to handle Promises instead of direct mutations

## Example: Complete Issue Management Component

```typescript
import React, { useState } from 'react';
import { useIssues, CreateIssueInput } from './useIssues';

function IssueManager() {
  const {
    issues,
    issuesByStatus,
    loading,
    error,
    addIssue,
    updateIssueStatus,
    deleteIssue,
    searchIssues,
  } = useIssues();

  const [searchQuery, setSearchQuery] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  const handleCreateIssue = async (issueData: CreateIssueInput) => {
    setIsCreating(true);
    try {
      await addIssue(issueData);
      // Issue created successfully
    } catch (error) {
      console.error('Failed to create issue:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleStatusChange = async (issueId: string, newStatus: Status) => {
    try {
      await updateIssueStatus(issueId, newStatus);
      // Status updated successfully
    } catch (error) {
      console.error('Failed to update status:', error);
    }
  };

  const searchResults = searchQuery ? searchIssues(searchQuery) : issues;

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <input
        type="text"
        placeholder="Search issues..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
      />
      
      <div>
        {searchResults.map(issue => (
          <div key={issue.id}>
            <h3>{issue.title}</h3>
            <p>{issue.description}</p>
            <span>Status: {issue.status?.name}</span>
            <span>Priority: {issue.priority?.name}</span>
            <span>Assignee: {issue.assignee?.name || 'Unassigned'}</span>
            <button onClick={() => handleStatusChange(issue.id, newStatus)}>
              Change Status
            </button>
            <button onClick={() => deleteIssue(issue.id)}>
              Delete
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
```

This hook provides a complete, production-ready solution for managing issues with Supabase, maintaining all the functionality of the original Circle project while adding real-time capabilities and proper error handling.
