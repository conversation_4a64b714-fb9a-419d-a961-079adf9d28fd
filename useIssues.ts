'use client';

import { useCallback, useEffect, useState } from 'react';
import { supabaseClient } from '@/lib/supabase/auth/client';
import type { RealtimeChannel } from '@supabase/supabase-js';

const supabase = supabaseClient;

// TypeScript Interfaces
export interface User {
   id: string;
   name: string;
   email: string;
   avatar_url: string | null;
   status: 'online' | 'offline' | 'away';
   role: 'Member' | 'Admin' | 'Guest';
   joined_date: string;
}

export interface Status {
   id: string;
   name: string;
   color: string;
   icon_name: string | null;
   sort_order: number;
}

export interface Priority {
   id: string;
   name: string;
   icon_name: string | null;
   sort_order: number;
}

export interface Label {
   id: string;
   name: string;
   color: string;
}

export interface Project {
   id: string;
   name: string;
   description: string | null;
   icon: string | null;
   percent_complete: number;
   start_date: string | null;
   target_date: string | null;
   lead_id: string | null;
   status_id: string | null;
   priority_id: string | null;
   health_id: 'no-update' | 'off-track' | 'on-track' | 'at-risk' | null;
   lead?: User;
   status?: Status;
   priority?: Priority;
}

export interface Cycle {
   id: string;
   number: number;
   name: string;
   team_id: string;
   start_date: string;
   end_date: string;
   progress: number;
}

export interface Issue {
   id: string;
   identifier: string;
   title: string;
   description: string;
   status_id: string;
   assignee_id: string | null;
   priority_id: string;
   project_id: string | null;
   cycle_id: string | null;
   parent_issue_id: string | null;
   rank: string;
   due_date: string | null;
   created_at: string;
   updated_at: string;
   created_by: string;
   // Joined data
   status?: Status;
   assignee?: User;
   priority?: Priority;
   project?: Project;
   cycle?: Cycle;
   labels?: Label[];
   subissues?: string[];
}

export interface CreateIssueInput {
   title: string;
   description?: string;
   status_id: string;
   assignee_id?: string | null;
   priority_id: string;
   project_id?: string | null;
   cycle_id?: string | null;
   parent_issue_id?: string | null;
   rank: string;
   due_date?: string | null;
   labels?: string[];
}

export interface FilterOptions {
   status?: string[];
   assignee?: string[];
   priority?: string[];
   labels?: string[];
   project?: string[];
}

export interface IssuesState {
   issues: Issue[];
   issuesByStatus: Record<string, Issue[]>;
   loading: boolean;
   error: string | null;
}

export function useIssues() {
   const [state, setState] = useState<IssuesState>({
      issues: [],
      issuesByStatus: {},
      loading: true,
      error: null,
   });

   const [realtimeChannel, setRealtimeChannel] = useState<RealtimeChannel | null>(null);

   // Helper function to group issues by status
   const groupIssuesByStatus = useCallback((issues: Issue[]): Record<string, Issue[]> => {
      return issues.reduce(
         (acc, issue) => {
            const statusId = issue.status_id;
            if (!acc[statusId]) {
               acc[statusId] = [];
            }
            acc[statusId].push(issue);
            return acc;
         },
         {} as Record<string, Issue[]>
      );
   }, []);

   // Helper function to build the complete query with joins
   const buildIssueQuery = useCallback(() => {
      return supabase.from('issues').select(`
        *,
        status:status_id (
          id,
          name,
          color,
          icon_name,
          sort_order
        ),
        assignee:assignee_id (
          id,
          name,
          email,
          avatar_url,
          status,
          role,
          joined_date
        ),
        priority:priority_id (
          id,
          name,
          icon_name,
          sort_order
        ),
        project:project_id (
          id,
          name,
          description,
          icon,
          percent_complete,
          start_date,
          target_date,
          lead_id,
          status_id,
          priority_id,
          health_id
        ),
        cycle:cycle_id (
          id,
          number,
          name,
          team_id,
          start_date,
          end_date,
          progress
        ),
        issue_labels (
          label:label_id (
            id,
            name,
            color
          )
        )
      `);
   }, []);

   // Transform the data to match the expected format
   const transformIssueData = useCallback((data: any[]): Issue[] => {
      return data.map((item) => ({
         ...item,
         status: item.status,
         assignee: item.assignee,
         priority: item.priority,
         project: item.project,
         cycle: item.cycle,
         labels: item.issue_labels?.map((il: any) => il.label) || [],
         subissues: [], // TODO: Implement subissues query if needed
      }));
   }, []);

   // Data Fetching Functions (return data directly)
   const getAllIssues = useCallback((): Issue[] => {
      return state.issues;
   }, [state.issues]);

   const getIssueById = useCallback(
      (id: string): Issue | undefined => {
         return state.issues.find((issue) => issue.id === id);
      },
      [state.issues]
   );

   const getIssuesByStatus = useCallback(
      (statusId: string): Issue[] => {
         return state.issues.filter((issue) => issue.status_id === statusId);
      },
      [state.issues]
   );

   const searchIssues = useCallback(
      (query: string): Issue[] => {
         if (!query.trim()) return [];

         const lowercaseQuery = query.toLowerCase();
         return state.issues.filter(
            (issue) =>
               issue.title.toLowerCase().includes(lowercaseQuery) ||
               issue.description.toLowerCase().includes(lowercaseQuery) ||
               issue.identifier.toLowerCase().includes(lowercaseQuery)
         );
      },
      [state.issues]
   );

   const filterIssues = useCallback(
      (filters: FilterOptions): Issue[] => {
         let filteredIssues = state.issues;

         // Filter by status
         if (filters.status && filters.status.length > 0) {
            filteredIssues = filteredIssues.filter((issue) =>
               filters.status!.includes(issue.status_id)
            );
         }

         // Filter by assignee
         if (filters.assignee && filters.assignee.length > 0) {
            filteredIssues = filteredIssues.filter((issue) => {
               if (filters.assignee!.includes('unassigned')) {
                  if (issue.assignee_id === null) {
                     return true;
                  }
               }
               return issue.assignee_id && filters.assignee!.includes(issue.assignee_id);
            });
         }

         // Filter by priority
         if (filters.priority && filters.priority.length > 0) {
            filteredIssues = filteredIssues.filter((issue) =>
               filters.priority!.includes(issue.priority_id)
            );
         }

         // Filter by labels
         if (filters.labels && filters.labels.length > 0) {
            filteredIssues = filteredIssues.filter((issue) =>
               issue.labels?.some((label) => filters.labels!.includes(label.id))
            );
         }

         // Filter by project
         if (filters.project && filters.project.length > 0) {
            filteredIssues = filteredIssues.filter(
               (issue) => issue.project_id && filters.project!.includes(issue.project_id)
            );
         }

         return filteredIssues;
      },
      [state.issues]
   );

   // Fetch all issues from database
   const fetchIssues = useCallback(async () => {
      try {
         setState((prev) => ({ ...prev, loading: true, error: null }));

         const { data, error } = await buildIssueQuery().order('rank', { ascending: false });

         if (error) throw error;

         const transformedIssues = transformIssueData(data || []);
         const groupedIssues = groupIssuesByStatus(transformedIssues);

         setState((prev) => ({
            ...prev,
            issues: transformedIssues,
            issuesByStatus: groupedIssues,
            loading: false,
         }));
      } catch (error) {
         setState((prev) => ({
            ...prev,
            loading: false,
            error: error instanceof Error ? error.message : 'Failed to fetch issues',
         }));
      }
   }, [buildIssueQuery, transformIssueData, groupIssuesByStatus]);

   // Action Functions (return Promise with resolve/reject pattern)
   const addIssue = useCallback(
      (issueInput: CreateIssueInput): Promise<Issue> => {
         return new Promise(async (resolve, reject) => {
            try {
               // Get current user
               const {
                  data: { user },
               } = await supabase.auth.getUser();
               if (!user) {
                  reject(new Error('User not authenticated'));
                  return;
               }

               // Generate unique identifier
               const identifier = `ISSUE-${Date.now()}`;

               const newIssue = {
                  ...issueInput,
                  identifier,
                  created_by: user.id,
               };

               const { data, error } = await supabase
                  .from('issues')
                  .insert([newIssue])
                  .select()
                  .single();

               if (error) throw error;

               // Add labels if provided
               if (issueInput.labels && issueInput.labels.length > 0) {
                  const labelInserts = issueInput.labels.map((labelId) => ({
                     issue_id: data.id,
                     label_id: labelId,
                  }));

                  await supabase.from('issue_labels').insert(labelInserts);
               }

               // Fetch the complete issue data
               const { data: completeIssue, error: fetchError } = await buildIssueQuery()
                  .eq('id', data.id)
                  .single();

               if (fetchError) throw fetchError;

               const transformedIssue = transformIssueData([completeIssue])[0];

               // Optimistic update
               setState((prev) => {
                  const newIssues = [...prev.issues, transformedIssue];
                  return {
                     ...prev,
                     issues: newIssues,
                     issuesByStatus: groupIssuesByStatus(newIssues),
                  };
               });

               resolve(transformedIssue);
            } catch (error) {
               reject(error instanceof Error ? error : new Error('Failed to create issue'));
            }
         });
      },
      [buildIssueQuery, transformIssueData, groupIssuesByStatus]
   );

   const updateIssue = useCallback(
      (id: string, updates: Partial<Issue>): Promise<Issue> => {
         return new Promise(async (resolve, reject) => {
            try {
               const { data, error } = await supabase
                  .from('issues')
                  .update(updates)
                  .eq('id', id)
                  .select()
                  .single();

               if (error) throw error;

               // Fetch the complete updated issue data
               const { data: completeIssue, error: fetchError } = await buildIssueQuery()
                  .eq('id', id)
                  .single();

               if (fetchError) throw fetchError;

               const transformedIssue = transformIssueData([completeIssue])[0];

               // Optimistic update
               setState((prev) => {
                  const newIssues = prev.issues.map((issue) =>
                     issue.id === id ? transformedIssue : issue
                  );
                  return {
                     ...prev,
                     issues: newIssues,
                     issuesByStatus: groupIssuesByStatus(newIssues),
                  };
               });

               resolve(transformedIssue);
            } catch (error) {
               reject(error instanceof Error ? error : new Error('Failed to update issue'));
            }
         });
      },
      [buildIssueQuery, transformIssueData, groupIssuesByStatus]
   );

   const deleteIssue = useCallback(
      (id: string): Promise<void> => {
         return new Promise(async (resolve, reject) => {
            try {
               const { error } = await supabase.from('issues').delete().eq('id', id);

               if (error) throw error;

               // Optimistic update
               setState((prev) => {
                  const newIssues = prev.issues.filter((issue) => issue.id !== id);
                  return {
                     ...prev,
                     issues: newIssues,
                     issuesByStatus: groupIssuesByStatus(newIssues),
                  };
               });

               resolve();
            } catch (error) {
               reject(error instanceof Error ? error : new Error('Failed to delete issue'));
            }
         });
      },
      [groupIssuesByStatus]
   );

   const updateIssueStatus = useCallback(
      (issueId: string, newStatus: Status): Promise<Issue> => {
         return new Promise(async (resolve, reject) => {
            try {
               const { data, error } = await supabase
                  .from('issues')
                  .update({ status_id: newStatus.id })
                  .eq('id', issueId)
                  .select()
                  .single();

               if (error) throw error;

               // Fetch the complete updated issue data
               const { data: completeIssue, error: fetchError } = await buildIssueQuery()
                  .eq('id', issueId)
                  .single();

               if (fetchError) throw fetchError;

               const transformedIssue = transformIssueData([completeIssue])[0];

               // Optimistic update
               setState((prev) => {
                  const newIssues = prev.issues.map((issue) =>
                     issue.id === issueId ? transformedIssue : issue
                  );
                  return {
                     ...prev,
                     issues: newIssues,
                     issuesByStatus: groupIssuesByStatus(newIssues),
                  };
               });

               resolve(transformedIssue);
            } catch (error) {
               reject(error instanceof Error ? error : new Error('Failed to update issue status'));
            }
         });
      },
      [buildIssueQuery, transformIssueData, groupIssuesByStatus]
   );

   const updateIssuePriority = useCallback(
      (issueId: string, newPriority: Priority): Promise<Issue> => {
         return new Promise(async (resolve, reject) => {
            try {
               const { data, error } = await supabase
                  .from('issues')
                  .update({ priority_id: newPriority.id })
                  .eq('id', issueId)
                  .select()
                  .single();

               if (error) throw error;

               // Fetch the complete updated issue data
               const { data: completeIssue, error: fetchError } = await buildIssueQuery()
                  .eq('id', issueId)
                  .single();

               if (fetchError) throw fetchError;

               const transformedIssue = transformIssueData([completeIssue])[0];

               // Optimistic update
               setState((prev) => {
                  const newIssues = prev.issues.map((issue) =>
                     issue.id === issueId ? transformedIssue : issue
                  );
                  return {
                     ...prev,
                     issues: newIssues,
                     issuesByStatus: groupIssuesByStatus(newIssues),
                  };
               });

               resolve(transformedIssue);
            } catch (error) {
               reject(
                  error instanceof Error ? error : new Error('Failed to update issue priority')
               );
            }
         });
      },
      [buildIssueQuery, transformIssueData, groupIssuesByStatus]
   );

   const updateIssueAssignee = useCallback(
      (issueId: string, assigneeId: string | null): Promise<Issue> => {
         return new Promise(async (resolve, reject) => {
            try {
               const { data, error } = await supabase
                  .from('issues')
                  .update({ assignee_id: assigneeId })
                  .eq('id', issueId)
                  .select()
                  .single();

               if (error) throw error;

               // Fetch the complete updated issue data
               const { data: completeIssue, error: fetchError } = await buildIssueQuery()
                  .eq('id', issueId)
                  .single();

               if (fetchError) throw fetchError;

               const transformedIssue = transformIssueData([completeIssue])[0];

               // Optimistic update
               setState((prev) => {
                  const newIssues = prev.issues.map((issue) =>
                     issue.id === issueId ? transformedIssue : issue
                  );
                  return {
                     ...prev,
                     issues: newIssues,
                     issuesByStatus: groupIssuesByStatus(newIssues),
                  };
               });

               resolve(transformedIssue);
            } catch (error) {
               reject(
                  error instanceof Error ? error : new Error('Failed to update issue assignee')
               );
            }
         });
      },
      [buildIssueQuery, transformIssueData, groupIssuesByStatus]
   );

   // Label management functions
   const addIssueLabel = useCallback(
      (issueId: string, labelId: string): Promise<void> => {
         return new Promise(async (resolve, reject) => {
            try {
               const { error } = await supabase
                  .from('issue_labels')
                  .insert([{ issue_id: issueId, label_id: labelId }]);

               if (error) throw error;

               // Refresh the issue data to get updated labels
               const { data: completeIssue, error: fetchError } = await buildIssueQuery()
                  .eq('id', issueId)
                  .single();

               if (fetchError) throw fetchError;

               const transformedIssue = transformIssueData([completeIssue])[0];

               // Optimistic update
               setState((prev) => {
                  const newIssues = prev.issues.map((issue) =>
                     issue.id === issueId ? transformedIssue : issue
                  );
                  return {
                     ...prev,
                     issues: newIssues,
                     issuesByStatus: groupIssuesByStatus(newIssues),
                  };
               });

               resolve();
            } catch (error) {
               reject(error instanceof Error ? error : new Error('Failed to add label to issue'));
            }
         });
      },
      [buildIssueQuery, transformIssueData, groupIssuesByStatus]
   );

   const removeIssueLabel = useCallback(
      (issueId: string, labelId: string): Promise<void> => {
         return new Promise(async (resolve, reject) => {
            try {
               const { error } = await supabase
                  .from('issue_labels')
                  .delete()
                  .eq('issue_id', issueId)
                  .eq('label_id', labelId);

               if (error) throw error;

               // Refresh the issue data to get updated labels
               const { data: completeIssue, error: fetchError } = await buildIssueQuery()
                  .eq('id', issueId)
                  .single();

               if (fetchError) throw fetchError;

               const transformedIssue = transformIssueData([completeIssue])[0];

               // Optimistic update
               setState((prev) => {
                  const newIssues = prev.issues.map((issue) =>
                     issue.id === issueId ? transformedIssue : issue
                  );
                  return {
                     ...prev,
                     issues: newIssues,
                     issuesByStatus: groupIssuesByStatus(newIssues),
                  };
               });

               resolve();
            } catch (error) {
               reject(
                  error instanceof Error ? error : new Error('Failed to remove label from issue')
               );
            }
         });
      },
      [buildIssueQuery, transformIssueData, groupIssuesByStatus]
   );

   const updateIssueProject = useCallback(
      (issueId: string, projectId: string | null): Promise<Issue> => {
         return new Promise(async (resolve, reject) => {
            try {
               const { data, error } = await supabase
                  .from('issues')
                  .update({ project_id: projectId })
                  .eq('id', issueId)
                  .select()
                  .single();

               if (error) throw error;

               // Fetch the complete updated issue data
               const { data: completeIssue, error: fetchError } = await buildIssueQuery()
                  .eq('id', issueId)
                  .single();

               if (fetchError) throw fetchError;

               const transformedIssue = transformIssueData([completeIssue])[0];

               // Optimistic update
               setState((prev) => {
                  const newIssues = prev.issues.map((issue) =>
                     issue.id === issueId ? transformedIssue : issue
                  );
                  return {
                     ...prev,
                     issues: newIssues,
                     issuesByStatus: groupIssuesByStatus(newIssues),
                  };
               });

               resolve(transformedIssue);
            } catch (error) {
               reject(error instanceof Error ? error : new Error('Failed to update issue project'));
            }
         });
      },
      [buildIssueQuery, transformIssueData, groupIssuesByStatus]
   );

   // Real-time subscriptions setup
   useEffect(() => {
      fetchIssues();

      // Set up real-time subscription
      const channel = supabase
         .channel('issues-changes')
         .on(
            'postgres_changes',
            {
               event: '*',
               schema: 'public',
               table: 'issues',
            },
            (payload) => {
               console.log('Real-time change received:', payload);
               // Refetch issues when changes occur
               fetchIssues();
            }
         )
         .on(
            'postgres_changes',
            {
               event: '*',
               schema: 'public',
               table: 'issue_labels',
            },
            (payload) => {
               console.log('Issue labels change received:', payload);
               // Refetch issues when label associations change
               fetchIssues();
            }
         )
         .subscribe();

      setRealtimeChannel(channel);

      return () => {
         if (channel) {
            supabase.removeChannel(channel);
         }
      };
   }, [fetchIssues]);

   // Cleanup on unmount
   useEffect(() => {
      return () => {
         if (realtimeChannel) {
            supabase.removeChannel(realtimeChannel);
         }
      };
   }, [realtimeChannel]);

   return {
      // State
      issues: state.issues,
      issuesByStatus: state.issuesByStatus,
      loading: state.loading,
      error: state.error,

      // Data fetching functions (return data directly)
      getAllIssues,
      getIssueById,
      getIssuesByStatus,
      searchIssues,
      filterIssues,

      // Action functions (return Promise with resolve/reject pattern)
      addIssue,
      updateIssue,
      deleteIssue,
      updateIssueStatus,
      updateIssuePriority,
      updateIssueAssignee,
      addIssueLabel,
      removeIssueLabel,
      updateIssueProject,

      // Utility functions
      fetchIssues,
   };
}
