/**
 * LexoRank Utilities for Issue Ordering
 * 
 * LexoRank is a ranking system that allows for efficient ordering of items
 * in a list without having to update all items when inserting between two items.
 * 
 * This implementation provides utilities for generating and managing LexoRank
 * strings for issue ordering in drag-and-drop scenarios.
 */

export class LexoRank {
  private static readonly MIN_CHAR = '0';
  private static readonly MAX_CHAR = 'z';
  private static readonly MID_CHAR = 'U';
  
  private value: string;

  constructor(value: string = LexoRank.MID_CHAR) {
    this.value = value;
  }

  /**
   * Create a LexoRank from a string value
   */
  static from(value: string): LexoRank {
    return new LexoRank(value);
  }

  /**
   * Generate the first rank (minimum)
   */
  static min(): LexoRank {
    return new LexoRank(LexoRank.MIN_CHAR);
  }

  /**
   * Generate the last rank (maximum)
   */
  static max(): LexoRank {
    return new LexoRank(LexoRank.MAX_CHAR);
  }

  /**
   * Generate a middle rank
   */
  static middle(): LexoRank {
    return new LexoRank(LexoRank.MID_CHAR);
  }

  /**
   * Generate a rank between two existing ranks
   */
  static between(prev: LexoRank | null, next: LexoRank | null): LexoRank {
    if (!prev && !next) {
      return LexoRank.middle();
    }
    
    if (!prev) {
      return LexoRank.from(next!.value).prev();
    }
    
    if (!next) {
      return LexoRank.from(prev.value).next();
    }

    return LexoRank.from(prev.value).between(LexoRank.from(next.value));
  }

  /**
   * Get the string representation
   */
  toString(): string {
    return this.value;
  }

  /**
   * Generate the next rank after this one
   */
  next(): LexoRank {
    return new LexoRank(this.increment(this.value));
  }

  /**
   * Generate the previous rank before this one
   */
  prev(): LexoRank {
    return new LexoRank(this.decrement(this.value));
  }

  /**
   * Generate a rank between this rank and another
   */
  between(other: LexoRank): LexoRank {
    const thisValue = this.value;
    const otherValue = other.value;
    
    if (thisValue >= otherValue) {
      throw new Error('Invalid rank order: first rank must be less than second rank');
    }

    return new LexoRank(this.generateBetween(thisValue, otherValue));
  }

  /**
   * Compare this rank with another
   */
  compareTo(other: LexoRank): number {
    return this.value.localeCompare(other.value);
  }

  /**
   * Check if this rank equals another
   */
  equals(other: LexoRank): boolean {
    return this.value === other.value;
  }

  /**
   * Increment a rank string
   */
  private increment(value: string): string {
    const chars = value.split('');
    let carry = true;
    
    for (let i = chars.length - 1; i >= 0 && carry; i--) {
      const charCode = chars[i].charCodeAt(0);
      
      if (charCode < LexoRank.MAX_CHAR.charCodeAt(0)) {
        chars[i] = String.fromCharCode(charCode + 1);
        carry = false;
      } else {
        chars[i] = LexoRank.MIN_CHAR;
      }
    }
    
    if (carry) {
      return LexoRank.MIN_CHAR + chars.join('');
    }
    
    return chars.join('');
  }

  /**
   * Decrement a rank string
   */
  private decrement(value: string): string {
    const chars = value.split('');
    let borrow = true;
    
    for (let i = chars.length - 1; i >= 0 && borrow; i--) {
      const charCode = chars[i].charCodeAt(0);
      
      if (charCode > LexoRank.MIN_CHAR.charCodeAt(0)) {
        chars[i] = String.fromCharCode(charCode - 1);
        borrow = false;
      } else {
        chars[i] = LexoRank.MAX_CHAR;
      }
    }
    
    return chars.join('');
  }

  /**
   * Generate a rank between two rank strings
   */
  private generateBetween(prev: string, next: string): string {
    const maxLength = Math.max(prev.length, next.length);
    const prevPadded = prev.padEnd(maxLength, LexoRank.MIN_CHAR);
    const nextPadded = next.padEnd(maxLength, LexoRank.MIN_CHAR);
    
    let result = '';
    let carry = 0;
    
    for (let i = 0; i < maxLength; i++) {
      const prevChar = prevPadded.charCodeAt(i);
      const nextChar = nextPadded.charCodeAt(i);
      const mid = Math.floor((prevChar + nextChar + carry) / 2);
      
      result += String.fromCharCode(mid);
      
      if (mid === prevChar) {
        carry = 1;
      } else {
        carry = 0;
        break;
      }
    }
    
    // If we couldn't find a middle value, append a character
    if (carry === 1) {
      result += LexoRank.MID_CHAR;
    }
    
    return result;
  }
}

/**
 * Utility functions for working with LexoRank in the context of issues
 */
export class IssueRankUtils {
  /**
   * Generate a rank for a new issue at the top of a status column
   */
  static getTopRank(existingIssues: Array<{ rank: string }>): string {
    if (existingIssues.length === 0) {
      return LexoRank.middle().toString();
    }

    // Sort by rank to find the highest
    const sorted = existingIssues
      .map(issue => LexoRank.from(issue.rank))
      .sort((a, b) => b.compareTo(a));

    const topRank = sorted[0];
    return topRank.next().toString();
  }

  /**
   * Generate a rank for a new issue at the bottom of a status column
   */
  static getBottomRank(existingIssues: Array<{ rank: string }>): string {
    if (existingIssues.length === 0) {
      return LexoRank.middle().toString();
    }

    // Sort by rank to find the lowest
    const sorted = existingIssues
      .map(issue => LexoRank.from(issue.rank))
      .sort((a, b) => a.compareTo(b));

    const bottomRank = sorted[0];
    return bottomRank.prev().toString();
  }

  /**
   * Generate a rank for inserting an issue between two others
   */
  static getRankBetween(
    prevIssue: { rank: string } | null,
    nextIssue: { rank: string } | null
  ): string {
    const prevRank = prevIssue ? LexoRank.from(prevIssue.rank) : null;
    const nextRank = nextIssue ? LexoRank.from(nextIssue.rank) : null;
    
    return LexoRank.between(prevRank, nextRank).toString();
  }

  /**
   * Sort issues by their rank
   */
  static sortByRank<T extends { rank: string }>(issues: T[]): T[] {
    return issues.sort((a, b) => {
      const rankA = LexoRank.from(a.rank);
      const rankB = LexoRank.from(b.rank);
      return rankB.compareTo(rankA); // Descending order (newest first)
    });
  }

  /**
   * Generate a rank for moving an issue to a specific position in a list
   */
  static getRankForPosition(
    issues: Array<{ rank: string }>,
    targetIndex: number
  ): string {
    const sortedIssues = IssueRankUtils.sortByRank(issues);
    
    if (targetIndex <= 0) {
      return IssueRankUtils.getTopRank(sortedIssues);
    }
    
    if (targetIndex >= sortedIssues.length) {
      return IssueRankUtils.getBottomRank(sortedIssues);
    }
    
    const prevIssue = sortedIssues[targetIndex - 1];
    const nextIssue = sortedIssues[targetIndex];
    
    return IssueRankUtils.getRankBetween(prevIssue, nextIssue);
  }

  /**
   * Validate that a rank string is valid
   */
  static isValidRank(rank: string): boolean {
    if (!rank || typeof rank !== 'string') {
      return false;
    }
    
    // Check that all characters are valid
    for (const char of rank) {
      const code = char.charCodeAt(0);
      if (code < LexoRank.MIN_CHAR.charCodeAt(0) || code > LexoRank.MAX_CHAR.charCodeAt(0)) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * Generate initial ranks for a batch of issues
   */
  static generateInitialRanks(count: number): string[] {
    const ranks: string[] = [];
    let currentRank = LexoRank.middle();
    
    for (let i = 0; i < count; i++) {
      ranks.push(currentRank.toString());
      currentRank = currentRank.next();
    }
    
    return ranks;
  }
}

/**
 * Example usage:
 * 
 * // Create initial rank
 * const firstRank = LexoRank.middle().toString(); // "U"
 * 
 * // Create rank for new item at top
 * const topRank = LexoRank.from(firstRank).next().toString(); // "V"
 * 
 * // Create rank between two items
 * const betweenRank = LexoRank.between(
 *   LexoRank.from("U"), 
 *   LexoRank.from("V")
 * ).toString(); // "UU" (approximately)
 * 
 * // For drag and drop scenarios:
 * const newRank = IssueRankUtils.getRankBetween(
 *   { rank: "U" }, // previous issue
 *   { rank: "V" }  // next issue
 * ); // Returns rank between U and V
 */
